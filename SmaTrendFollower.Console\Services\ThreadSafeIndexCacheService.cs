using Microsoft.EntityFrameworkCore;
using SmaTrendFollower.Data;
using SmaTrendFollower.Interfaces;
using SmaTrendFollower.Services;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Console.Services;

/// <summary>
/// Thread-safe implementation of IndexCacheService using DbContextFactory
/// Fixes the critical threading issues that were causing system failures
/// </summary>
public class ThreadSafeIndexCacheService : IIndexCacheService
{
    private readonly IDbContextFactory<IndexCacheDbContext> _contextFactory;
    private readonly ILogger<ThreadSafeIndexCacheService> _logger;

    public ThreadSafeIndexCacheService(
        IDbContextFactory<IndexCacheDbContext> contextFactory,
        ILogger<ThreadSafeIndexCacheService> logger)
    {
        _contextFactory = contextFactory;
        _logger = logger;
    }

    public async Task<IEnumerable<IndexBar>> GetCachedBarsAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            
            _logger.LogDebug("Retrieving cached index bars for {Symbol} from {StartDate} to {EndDate}", 
                symbol, startDate, endDate);

            var cachedBars = await context.CachedIndexBars
                .Where(b => b.Symbol == symbol && 
                           b.TimeUtc >= startDate && 
                           b.TimeUtc <= endDate)
                .OrderBy(b => b.TimeUtc)
                .ToListAsync();

            _logger.LogDebug("Found {Count} cached index bars for {Symbol}", cachedBars.Count, symbol);

            return cachedBars.Select(b => b.ToIndexBar()).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached index bars for {Symbol}", symbol);
            return Enumerable.Empty<IndexBar>();
        }
    }

    public async Task CacheBarsAsync(string symbol, IEnumerable<IndexBar> indexBars)
    {
        try
        {
            var barsList = indexBars.ToList();
            if (!barsList.Any())
            {
                _logger.LogDebug("No index bars to cache for {Symbol}", symbol);
                return;
            }

            _logger.LogDebug("Caching {Count} index bars for {Symbol}", barsList.Count, symbol);

            using var context = await _contextFactory.CreateDbContextAsync();
            await context.AddOrUpdateCachedBarsAsync(symbol, barsList);

            _logger.LogInformation("Successfully cached {Count} index bars for {Symbol}", barsList.Count, symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching index bars for {Symbol}", symbol);
            throw;
        }
    }

    public async Task<bool> HasCachedBarsAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            
            var hasData = await context.CachedIndexBars
                .AnyAsync(b => b.Symbol == symbol && 
                              b.TimeUtc >= startDate && 
                              b.TimeUtc <= endDate);

            _logger.LogDebug("Index cache check for {Symbol}: {HasData}", symbol, hasData);
            return hasData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cached index bars for {Symbol}", symbol);
            return false;
        }
    }

    public async Task<DateTime?> GetLatestBarDateAsync(string symbol)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            
            var latestDate = await context.CachedIndexBars
                .Where(b => b.Symbol == symbol)
                .MaxAsync(b => (DateTime?)b.TimeUtc);

            _logger.LogDebug("Latest index bar date for {Symbol}: {LatestDate}", symbol, latestDate);
            return latestDate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting latest index bar date for {Symbol}", symbol);
            return null;
        }
    }

    public async Task ClearCacheAsync(string symbol)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            
            var barsToRemove = context.CachedIndexBars
                .Where(b => b.Symbol == symbol);

            context.CachedIndexBars.RemoveRange(barsToRemove);
            await context.SaveChangesAsync();

            _logger.LogInformation("Cleared index cache for {Symbol}", symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing index cache for {Symbol}", symbol);
            throw;
        }
    }

    public async Task<long> GetCacheSizeAsync()
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.CachedIndexBars.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting index cache size");
            return 0;
        }
    }

    public async Task<IEnumerable<string>> GetCachedSymbolsAsync()
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            return await context.CachedIndexBars
                .Select(b => b.Symbol)
                .Distinct()
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cached index symbols");
            return Enumerable.Empty<string>();
        }
    }

    public async Task<(DateTime startDate, DateTime endDate)?> GetMissingDateRangeAsync(string symbol, DateTime requestedStartDate, DateTime requestedEndDate)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var latestDate = await context.CachedIndexBars
                .Where(b => b.Symbol == symbol)
                .MaxAsync(b => (DateTime?)b.TimeUtc);

            if (latestDate == null)
            {
                // No data cached, need to fetch entire range
                return (requestedStartDate, requestedEndDate);
            }

            if (latestDate >= requestedEndDate)
            {
                // Cache is up to date
                return null;
            }

            // Need to fetch from latest cached date to requested end date
            var fetchStartDate = latestDate.Value.AddDays(1);

            // CRITICAL FIX: Prevent startDate > endDate scenario
            if (fetchStartDate > requestedEndDate)
            {
                // Cache is actually up to date, no fetch needed
                return null;
            }

            return (fetchStartDate, requestedEndDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error determining missing date range for {Symbol}", symbol);
            return (requestedStartDate, requestedEndDate);
        }
    }

    public async Task<DateTime?> GetLatestCachedDateAsync(string symbol)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            return await context.CachedIndexBars
                .Where(b => b.Symbol == symbol)
                .MaxAsync(b => (DateTime?)b.TimeUtc);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting latest cached date for {Symbol}", symbol);
            return null;
        }
    }

    public async Task<bool> IsCacheFreshAsync(string symbol, DateTime requestedEndDate)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var latestDate = await context.CachedIndexBars
                .Where(b => b.Symbol == symbol)
                .MaxAsync(b => (DateTime?)b.TimeUtc);

            return latestDate >= requestedEndDate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache freshness for {Symbol}", symbol);
            return false;
        }
    }

    public async Task PerformMaintenanceAsync(int retainDays = 365)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var cutoffDate = DateTime.UtcNow.AddDays(-retainDays);
            var oldBars = context.CachedIndexBars.Where(b => b.TimeUtc < cutoffDate);

            context.CachedIndexBars.RemoveRange(oldBars);
            await context.SaveChangesAsync();

            _logger.LogInformation("Cache maintenance completed for index bars, removed data older than {CutoffDate}", cutoffDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache maintenance");
            throw;
        }
    }

    public async Task InitializeCacheAsync()
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            await context.Database.EnsureCreatedAsync();
            _logger.LogDebug("Index cache database initialized");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing cache");
            throw;
        }
    }
}
